from celery import current_app
from django.utils import timezone
from dxh_common.logger import Logger
from dxh_common.base.base_service import ServiceError

from apps.user.tasks import delete_user_after_delay

logger = Logger(__name__)


class AccountDeletionService:
    def __init__(self):
        pass

    def request_account_deletion(self, user, days_until_deletion):
        try:
            self.cancel_deletion_request(user)

            countdown = days_until_deletion * 24 * 60 * 60

            task_result = delete_user_after_delay.apply_async(
                args=[user.id],
                countdown=countdown
            )

            logger.info(f"Account deletion scheduled for user {user.id} in {days_until_deletion} days, "
                       f"task ID: {task_result.id}")

            data = {
                'task_id': task_result.id,
                'scheduled_in_days': days_until_deletion,
                'scheduled_at': timezone.now() + timezone.timedelta(days=days_until_deletion),
                'message': f'Account will be deleted in {days_until_deletion} days. Log in to cancel.'
            }

            return data

        except Exception as e:
            logger.error(f"Error requesting account deletion for user {user.id}: {str(e)}")
            raise ServiceError(f"Failed to request account deletion: {str(e)}")

    def cancel_deletion_request(self, user, reason='manual_cancellation'):
        try:
            # Get active tasks from Celery inspect
            inspect = current_app.control.inspect()

            # Get all active, scheduled, and reserved tasks
            active_tasks = inspect.active() or {}
            scheduled_tasks = inspect.scheduled() or {}
            reserved_tasks = inspect.reserved() or {}

            revoked_count = 0

            # Check all task types for our specific task
            all_tasks = []
            for _, tasks in active_tasks.items():
                all_tasks.extend(tasks)
            for _, tasks in scheduled_tasks.items():
                all_tasks.extend(tasks)
            for _, tasks in reserved_tasks.items():
                all_tasks.extend(tasks)

            # Find tasks that match our criteria
            for task_info in all_tasks:
                # Get task ID - it might be in 'id' or 'uuid' field
                task_id = task_info.get('id') or task_info.get('uuid')

                if (task_id and
                    task_info.get('name') == 'apps.user.tasks.delete_user_after_delay' and
                    task_info.get('args') and len(task_info['args']) > 0 and
                    task_info['args'][0] == user.id):

                    current_app.control.revoke(task_id, terminate=True)
                    revoked_count += 1
                    logger.info(f"Revoked deletion task {task_id} for user {user.id}")

            if revoked_count > 0:
                logger.info(f"Cancelled {revoked_count} deletion task(s) for user {user.id}, reason: {reason}")
                return True
            else:
                logger.info(f"No pending deletion tasks found for user {user.id}")
                return False

        except Exception as e:
            logger.error(f"Error cancelling deletion request for user {user.id}: {str(e)}")
            return False


    def cleanup_all_user_deletion_tasks(self, user):
        try:
            # Get active tasks from Celery inspect
            inspect = current_app.control.inspect()

            # Get all active, scheduled, and reserved tasks
            active_tasks = inspect.active() or {}
            scheduled_tasks = inspect.scheduled() or {}
            reserved_tasks = inspect.reserved() or {}

            total_cleaned = 0

            # Check all task types for our specific task
            all_tasks = []
            for _, tasks in active_tasks.items():
                all_tasks.extend(tasks)
            for _, tasks in scheduled_tasks.items():
                all_tasks.extend(tasks)
            for _, tasks in reserved_tasks.items():
                all_tasks.extend(tasks)

            # Find and revoke tasks that match our criteria
            for task_info in all_tasks:
                # Get task ID - it might be in 'id' or 'uuid' field
                task_id = task_info.get('id') or task_info.get('uuid')

                if (task_id and
                    task_info.get('name') == 'apps.user.tasks.delete_user_after_delay' and
                    task_info.get('args') and len(task_info['args']) > 0 and
                    task_info['args'][0] == user.id):

                    # Revoke the task
                    current_app.control.revoke(task_id, terminate=True)
                    total_cleaned += 1
                    logger.info(f"Cleaned up deletion task {task_id} for user {user.id}")

            logger.info(f"Cleaned up {total_cleaned} deletion task(s) for user {user.id}")
            return total_cleaned

        except Exception as e:
            logger.error(f"Error cleaning up deletion tasks for user {user.id}: {str(e)}")
            return 0
