from celery.task.control import revoke
from django.utils import timezone
from dxh_common.logger import Logger
from dxh_common.base.base_service import ServiceError

from config.extend.celery_setting import app as celery_app
from apps.user.tasks import delete_user_after_delay

logger = Logger(__name__)


class AccountDeletionService:
    def __init__(self):
        pass

    def request_account_deletion(self, user, days_until_deletion):
        try:
            self.cancel_deletion_request(user)

            # Calculate countdown in seconds
            countdown = days_until_deletion * 24 * 60 * 60

            # Schedule the Celery task
            task_result = delete_user_after_delay.apply_async(
                args=[user.id],
                countdown=countdown
            )

            logger.info(f"Account deletion scheduled for user {user.id} in {days_until_deletion} days, "
                       f"task ID: {task_result.id}")

            return {
                'task_id': task_result.id,
                'scheduled_in_days': days_until_deletion,
                'scheduled_at': timezone.now() + timezone.timedelta(days=days_until_deletion),
                'message': f'Account will be deleted in {days_until_deletion} days. Log in to cancel.'
            }

        except Exception as e:
            logger.error(f"Error requesting account deletion for user {user.id}: {str(e)}")
            raise ServiceError(f"Failed to request account deletion: {str(e)}")

    def cancel_deletion_request(self, user, reason='manual_cancellation'):
        try:
            

            # Find pending deletion tasks for this user
            tasks = celery_app.backend.get_many(
                {"state": "PENDING"},
                limit=100,
                timeout=1,
                task="apps.user.tasks.delete_user_after_delay",
                args=[user.id]
            )

            revoked_count = 0
            for task_id, task in tasks.items():
                revoke(task_id, terminate=True)
                revoked_count += 1
                logger.info(f"Revoked deletion task {task_id} for user {user.id}")

            if revoked_count > 0:
                logger.info(f"Cancelled {revoked_count} deletion task(s) for user {user.id}, reason: {reason}")
                return True
            else:
                logger.info(f"No pending deletion tasks found for user {user.id}")
                return False

        except Exception as e:
            logger.error(f"Error cancelling deletion request for user {user.id}: {str(e)}")
            return False

    def get_deletion_status(self, user):
        """
        Get deletion status for a user by checking Celery backend
        """
        try:
            from config.extend.celery_setting import app as celery_app

            # Find pending deletion tasks for this user
            tasks = celery_app.backend.get_many(
                {"state": "PENDING"},
                limit=100,
                timeout=1,
                task="apps.user.tasks.delete_user_after_delay",
                args=[user.id]
            )

            if not tasks:
                return {
                    'has_pending_deletion': False,
                    'pending_tasks': []
                }

            pending_tasks = []
            for task_id, task in tasks.items():
                # Get task details
                from celery.result import AsyncResult
                result = AsyncResult(task_id, app=celery_app)

                pending_tasks.append({
                    'task_id': task_id,
                    'state': result.state,
                    'eta': getattr(result, 'eta', None),
                })

            return {
                'has_pending_deletion': len(pending_tasks) > 0,
                'pending_tasks': pending_tasks,
                'task_count': len(pending_tasks)
            }

        except Exception as e:
            logger.error(f"Error getting deletion status for user {user.id}: {str(e)}")
            return {
                'has_pending_deletion': False,
                'pending_tasks': [],
                'error': str(e)
            }

    def cleanup_all_user_deletion_tasks(self, user):
        """
        Cleanup all deletion tasks for a user (including completed/failed ones)
        """
        try:
            from config.extend.celery_setting import app as celery_app

            # Find all tasks for this user (any state)
            all_states = ["PENDING", "STARTED", "SUCCESS", "FAILURE", "RETRY", "REVOKED"]
            total_cleaned = 0

            for state in all_states:
                tasks = celery_app.backend.get_many(
                    {"state": state},
                    limit=100,
                    timeout=1,
                    task="apps.user.tasks.delete_user_after_delay",
                    args=[user.id]
                )

                for task_id, task in tasks.items():
                    # For pending tasks, revoke them
                    if state == "PENDING":
                        from celery.task.control import revoke
                        revoke(task_id, terminate=True)

                    total_cleaned += 1

            logger.info(f"Cleaned up {total_cleaned} deletion task(s) for user {user.id}")
            return total_cleaned

        except Exception as e:
            logger.error(f"Error cleaning up deletion tasks for user {user.id}: {str(e)}")
            return 0
