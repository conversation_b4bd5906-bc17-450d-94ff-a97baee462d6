from typing import Any, Dict
from django.utils import timezone
from django.db import models
from dxh_common.logger import Logger
from dxh_common.base.base_api_view import BaseApiView
from dxh_libraries.rest_framework import Response, status
from dxh_libraries.translation import gettext_lazy as _

from apps.identity.services import UserService
from apps.core.services import SystemSettingService
from apps.user.api.v1.serializers import UserDetailSerializer
from apps.user.services import AccountDeletionService

logger = Logger(__name__)


class RequestDeletionView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.user_service = UserService()
        self.system_settings_service = SystemSettingService()
        self.account_deletion_service = AccountDeletionService()

    def post(self, request):
        try:
            user = self.user_service.get(id=request.user.id)
            if user != request.user:
                result = {
                    "message": _("Permission denied")
                }
                return Response(result, status=status.HTTP_403_FORBIDDEN)

            system_settings = self.system_settings_service.get(company=user.company)
            if not system_settings:
                result = {
                    "message": _("System settings not configured")
                }
                return Response(result, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            days_until_deletion = system_settings.days_until_account_deletion

            deletion_result = self.account_deletion_service.request_account_deletion(
                user=user,
                days_until_deletion=days_until_deletion
            )

            result = {
                "message": deletion_result['message'],
                "data": {
                    "task_id": deletion_result['task_id'],
                    "scheduled_in_days": deletion_result['scheduled_in_days'],
                    "scheduled_at": deletion_result['scheduled_at'].isoformat(),
                }
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "RequestDeletionView:post",
                        "message": "Unexpected error occurred", "error": str(e)})
            raise e


class ExportDataView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.user_service = UserService()

    def get(self, request):
        try:
            user = self.user_service.get(id=request.user.id)
            if user != request.user:
                result = {
                    "message": _("Permission denied")
                }
                return Response(result, status=status.HTTP_403_FORBIDDEN)

            # Collect comprehensive user data
            user_data = self._collect_user_data(user)

            result = {
                "message": _("Data export successful"),
                "data": user_data,
                "exported_at": timezone.now().isoformat(),
                "user_id": user.id,
                "user_email": user.email
            }
            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "ExportDataView:get",
                        "message": "Unexpected error occurred", "error": str(e)})
            raise e

    def _collect_user_data(self, user):
        """Collect all user-related data for export"""
        user_data = {
            # Basic profile information
            'profile': UserDetailSerializer(user).data,

            # User activity logs
            'activity_logs': self._get_activity_logs(user),

            # User sessions
            'sessions': self._get_user_sessions(user),

            # Notifications
            'notifications': self._get_notifications(user),

            # Kanban-related data
            'kanban_data': self._get_kanban_data(user),

            # User preferences and settings
            'preferences': self._get_user_preferences(user),

            # Security-related data
        }

        return user_data

    def _get_activity_logs(self, user):
        """Get user activity logs"""
        try:
            from apps.user.models import UserActivityLog
            activity_logs = UserActivityLog.objects.filter(user=user).order_by('-created_at')[:100]

            return [
                {
                    'id': log.id,
                    'action': log.action,
                    'ip_address': log.ip_address,
                    'user_agent': log.user_agent,
                    'session_key': log.session_key,
                    'duration': log.duration,
                    'latitude': log.latitude,
                    'longitude': log.longitude,
                    'details': log.details,
                    'created_at': log.created_at.isoformat() if log.created_at else None,
                }
                for log in activity_logs
            ]
        except Exception as e:
            logger.warning(f"Error getting activity logs: {str(e)}")
            return []

    def _get_user_sessions(self, user):
        """Get user session data"""
        try:
            from apps.identity.models import UserSession
            sessions = UserSession.objects.filter(user=user).order_by('-created_at')[:50]

            return [
                {
                    'id': session.id,
                    'ip_address': session.ip_address,
                    'location': session.location,
                    'browser': session.browser,
                    'browser_version': session.browser_version,
                    'login_time': session.login_time.isoformat() if session.login_time else None,
                    'logout_time': session.logout_time.isoformat() if session.logout_time else None,
                    'expires_at': session.expires_at.isoformat() if session.expires_at else None,
                    'is_active': session.is_active,
                }
                for session in sessions
            ]
        except Exception as e:
            logger.warning(f"Error getting user sessions: {str(e)}")
            return []

    def _get_notifications(self, user):
        """Get user notifications"""
        try:
            from apps.notification.models import InAppNotification, EmailNotification

            # In-app notifications
            in_app_notifications = InAppNotification.objects.filter(user=user).order_by('-created_at')[:100]

            # Email notifications
            email_notifications = EmailNotification.objects.filter(user=user).order_by('-created_at')[:100]

            return {
                'in_app_notifications': [
                    {
                        'id': notif.id,
                        'title': notif.title,
                        'message': notif.message,
                        'is_read': notif.is_read,
                        'created_at': notif.created_at.isoformat() if notif.created_at else None,
                    }
                    for notif in in_app_notifications
                ],
                'email_notifications': [
                    {
                        'id': notif.id,
                        'recipient_email': notif.recipient_email,
                        'subject': notif.subject,
                        'body': notif.body,
                        'created_at': notif.created_at.isoformat() if notif.created_at else None,
                    }
                    for notif in email_notifications
                ]
            }
        except Exception as e:
            logger.warning(f"Error getting notifications: {str(e)}")
            return {'in_app_notifications': [], 'email_notifications': []}

    def _get_kanban_data(self, user):
        """Get user's Kanban-related data"""
        try:
            from apps.kanban.models import Task, KanbanComment, Project

            # Tasks assigned to user
            assigned_tasks = Task.objects.filter(assignees=user).order_by('-created_at')[:100]

            # Tasks created by user
            created_tasks = Task.objects.filter(created_by=user).order_by('-created_at')[:100]

            # Comments by user
            comments = KanbanComment.objects.filter(created_by=user).order_by('-created_at')[:100]

            # Projects where user is involved
            projects = Project.objects.filter(
                models.Q(created_by=user) |
                models.Q(tasks__assignees=user)
            ).distinct().order_by('-created_at')[:50]

            return {
                'assigned_tasks': [
                    {
                        'id': task.id,
                        'title': task.title,
                        'description': task.description,
                        'status': task.status,
                        'priority': task.priority,
                        'created_at': task.created_at.isoformat() if task.created_at else None,
                        'due_date': task.due_date.isoformat() if task.due_date else None,
                    }
                    for task in assigned_tasks
                ],
                'created_tasks': [
                    {
                        'id': task.id,
                        'title': task.title,
                        'description': task.description,
                        'status': task.status,
                        'priority': task.priority,
                        'created_at': task.created_at.isoformat() if task.created_at else None,
                    }
                    for task in created_tasks
                ],
                'comments': [
                    {
                        'id': comment.id,
                        'content': comment.content,
                        'task_id': comment.task.id if comment.task else None,
                        'created_at': comment.created_at.isoformat() if comment.created_at else None,
                    }
                    for comment in comments
                ],
                'projects': [
                    {
                        'id': project.id,
                        'name': project.name,
                        'description': project.description,
                        'created_at': project.created_at.isoformat() if project.created_at else None,
                    }
                    for project in projects
                ]
            }
        except Exception as e:
            logger.warning(f"Error getting kanban data: {str(e)}")
            return {'assigned_tasks': [], 'created_tasks': [], 'comments': [], 'projects': []}

    def _get_user_preferences(self, user):
        """Get user preferences and settings"""
        try:
            from apps.user.models import UserPreference
            from apps.notification.models import UserNotificationPreference

            # User preferences
            user_pref = getattr(user, 'userpreference', None)
            preferences = {}
            if user_pref:
                preferences = {
                    'language': user_pref.language,
                    'timezone': user_pref.timezone,
                    'theme': user_pref.theme,
                    'date_format': user_pref.date_format,
                    'time_format': user_pref.time_format,
                }

            # Notification preferences
            notif_prefs = UserNotificationPreference.objects.filter(user=user)
            notification_preferences = [
                {
                    'notification_type': pref.notification_type,
                    'email_enabled': pref.email_enabled,
                    'sms_enabled': pref.sms_enabled,
                    'push_enabled': pref.push_enabled,
                    'in_app_enabled': pref.in_app_enabled,
                }
                for pref in notif_prefs
            ]

            return {
                'user_preferences': preferences,
                'notification_preferences': notification_preferences
            }
        except Exception as e:
            logger.warning(f"Error getting user preferences: {str(e)}")
            return {'user_preferences': {}, 'notification_preferences': []}