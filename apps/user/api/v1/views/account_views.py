from typing import Any, Dict
from django.utils import timezone
from dxh_common.logger import Logger
from dxh_common.base.base_api_view import BaseApiView
from dxh_libraries.rest_framework import Response, status
from dxh_libraries.translation import gettext_lazy as _

from apps.identity.services import UserService
from apps.core.services import SystemSettingService
from apps.user.api.v1.serializers import UserDetailSerializer
from apps.user.services import AccountDeletionService

logger = Logger(__name__)


class RequestDeletionView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.user_service = UserService()
        self.system_settings_service = SystemSettingService()
        self.account_deletion_service = AccountDeletionService()

    def post(self, request):
        try:
            user = self.user_service.get(id=request.user.id)
            if user != request.user:
                result = {
                    "message": _("Permission denied")
                }
                return Response(result, status=status.HTTP_403_FORBIDDEN)

            system_settings = self.system_settings_service.get(company=user.company)
            if not system_settings:
                result = {
                    "message": _("System settings not configured")
                }
                return Response(result, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            days_until_deletion = system_settings.days_until_account_deletion

            deletion_result = self.account_deletion_service.request_account_deletion(
                user=user,
                days_until_deletion=days_until_deletion
            )

            result = {
                "message": deletion_result['message'],
                "data": {
                    "task_id": deletion_result['task_id'],
                    "scheduled_in_days": deletion_result['scheduled_in_days'],
                    "scheduled_at": deletion_result['scheduled_at'].isoformat(),
                }
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "RequestDeletionView:post",
                        "message": "Unexpected error occurred", "error": str(e)})
            raise e


class ExportDataView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.user_service = UserService()

    def get(self, request, user_id):
        user = self.user_service.get(id=request.user.id)
        if user != request.user and not request.user.is_staff:
            result = {
                "message": "Permission denied"
            }
            return Response(result, status=status.HTTP_403_FORBIDDEN)

        # Collect user data
        user_data = {
            'profile': UserDetailSerializer(user).data,
            # 'notifications': NotificationSerializer(
            #     user.notification_set.all(),
            #     many=True
            # ).data,
            # 'roles': RoleSerializer(
            #     user.roles.all(),
            #     many=True
            # ).data
        }

        result: Dict[str, Any] = {
            "message": "Data export successful",
            "data": user_data
        }
        return Response(result)


class CancelDeletionView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.user_service = UserService()
        self.account_deletion_service = AccountDeletionService()

    def post(self, request):
        try:
            user = self.user_service.get(id=request.user.id)
            if user != request.user:
                result = {
                    "message": _("Permission denied")
                }
                return Response(result, status=status.HTTP_403_FORBIDDEN)

            # Cancel deletion request
            cancelled = self.account_deletion_service.cancel_deletion_request(
                user=user,
                reason='manual_cancellation'
            )

            if cancelled:
                result = {
                    "message": _("Account deletion cancelled successfully"),
                    "data": {
                        "cancelled": True
                    }
                }
                return Response(result, status=status.HTTP_200_OK)
            else:
                result = {
                    "message": _("No pending deletion request found"),
                    "data": {
                        "cancelled": False
                    }
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

        except Exception as e:
            logger.error({"event": "CancelDeletionView:post",
                        "message": "Unexpected error occurred", "error": str(e)})
            raise e


class DeletionStatusView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.user_service = UserService()
        self.account_deletion_service = AccountDeletionService()

    def get(self, request):
        try:
            user = self.user_service.get(id=request.user.id)
            if user != request.user:
                result = {
                    "message": _("Permission denied")
                }
                return Response(result, status=status.HTTP_403_FORBIDDEN)

            # Get deletion status
            status_info = self.account_deletion_service.get_deletion_status(user)

            result = {
                "message": _("Deletion status retrieved successfully"),
                "data": status_info
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "DeletionStatusView:get",
                        "message": "Unexpected error occurred", "error": str(e)})
            raise e
