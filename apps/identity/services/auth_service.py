from django.utils import timezone
from django.db import transaction
from dxh_common.logger import Logger
from dxh_libraries.translation import gettext_lazy as _
from dxh_libraries.rest_framework_simplejwt import RefreshToken, AccessToken
from dxh_common.base.base_service import ServiceError
from dxh_common.utils.enums import VerificationTokenType

from apps.core.services.company_service import CompanyService
from apps.core.services.system_setting_service import SystemSettingService
from apps.cross_cutting.email_service import EmailService
from apps.identity.services.verification_code_service import VerificationCodeService
from apps.identity.services.verification_token_service import VerificationTokenService
from apps.identity.services.device_service import UserDeviceService
from apps.identity.services.session_service import UserSessionService
from apps.identity.services.user_service import UserService
from apps.user.services.account_deletion_service import AccountDeletionService
from apps.user.services.user_preference_service import UserPreferenceService

logger = Logger(__name__)


class AuthService:
    def __init__(self):
        self.verification_code_service = VerificationCodeService()
        self.verification_token_service = VerificationTokenService()
        self.system_setting_service = SystemSettingService()
        self.email_service = EmailService()
        self.user_device_service = UserDeviceService()
        self.user_session_service = UserSessionService()
        self.user_service = UserService()
        self.company_service = CompanyService()
        self.account_deletion_service = AccountDeletionService()
        self.user_preference_service = UserPreferenceService()

    def _is_mfa_enabled(self, user):
        try:
            preference = self.user_preference_service.get_user_preference(user)

            return preference.mfa_enabled if preference else False

        except Exception as e:
            logger.error(f"Error checking MFA status for user {user.pk}: {str(e)}")
            return False

    def _create_mfa_session(self, user, user_agent, ip_address):
        try:
            verification_token = self.verification_token_service.create_verification_token(
                user=user,
                token_type=VerificationTokenType.MFA_SESSION.value
            )

            logger.info(f"MFA session token created for user {user.pk}")
            return verification_token.token

        except Exception as e:
            logger.error(f"Error creating MFA session for user {user.pk}: {str(e)}")
            raise ServiceError(f"Failed to create MFA session: {str(e)}")

    def _get_mfa_session(self, mfa_token):
        try:
            verification_token = self.verification_token_service.verify_token(
                token=mfa_token,
                token_type=VerificationTokenType.MFA_SESSION.value
            )

            if verification_token:
                return {
                    'user_id': verification_token.user.id,
                    'token_obj': verification_token
                }
            return None

        except Exception as e:
            logger.error(f"Error retrieving MFA session: {str(e)}")
            return None

    def _delete_mfa_session(self, mfa_token):
        try:
            verification_token = self.verification_token_service.get(
                token=mfa_token,
                token_type=VerificationTokenType.MFA_SESSION.value,
                is_used=False
            )

            if verification_token:
                self.verification_token_service.mark_used(verification_token)
                logger.info(f"MFA session token marked as used")

        except Exception as e:
            logger.error(f"Error deleting MFA session: {str(e)}")
            raise ServiceError(f"Failed to delete MFA session: {str(e)}")

    @transaction.atomic
    def login(self, user_payload):
        try:
            email = user_payload.get("email")
            password = user_payload.get("password")
            user_agent = user_payload.get("user_agent")
            ip_address = user_payload.get("ip_address")

            user = self.user_service.get(email=email)

            if not user:
                return None, None, _("Invalid credentials")

            if not user.is_active:
                return None, None, _("User is not active")

            if not user.check_password(password):
                return None, None, _("Invalid credentials")

            # Cancel any pending account deletion requests
            try:
                cancelled = self.account_deletion_service.cancel_deletion_request(
                    user=user,
                    reason='user_login'
                )
                if cancelled:
                    logger.info(f"Cancelled pending account deletion for user {user.id} due to login")
            except Exception as deletion_error:
                # Don't fail login if deletion cancellation fails
                logger.error(f"Failed to cancel deletion request for user {user.id}: {str(deletion_error)}")

            if self._is_mfa_enabled(user):
                mfa_token = self._create_mfa_session(user, user_agent, ip_address)
                mfa_data = {
                    "mfa_required": True,
                    "mfa_token": mfa_token
                }
                return user, mfa_data, _("MFA verification required")

            tokens = self.get_tokens(user)

            # user_device = self.user_device_service.create_user_device(
            #     user=user, user_agent=user_agent
            # )
            # user_session = self.user_session_service.create_user_session(
            #     user, user_agent, user_device, ip_address, tokens
            # )

            return user, tokens, _("Login successful")

        except Exception as e:
            logger.error(f"Error logging in user with email {email}: {str(e)}")
            raise ServiceError(f"Error logging in: {str(e)}")

    def complete_mfa_login(self, mfa_token):
        try:
            session_data = self._get_mfa_session(mfa_token)
            if not session_data:
                return None, None, _("Invalid or expired MFA session")

            user = self.user_service.get(id=session_data['user_id'])
            if not user:
                return None, None, _("User not found")

            tokens = self.get_tokens(user)

            self._delete_mfa_session(mfa_token)

            return user, tokens, _("Login completed successfully")

        except Exception as e:
            logger.error(f"Error completing MFA login: {str(e)}")
            raise ServiceError(f"Error completing MFA login: {str(e)}")

    @transaction.atomic
    def logout(self, user_payload):
        try:
            user = user_payload["user"]
            refresh_token = user_payload["refresh_token"]
            refresh_token = RefreshToken(refresh_token)
            refresh_token.blacklist()

            user_agent = user_payload.get("user_agent")
            user_session_data = {
                'user': user,
                'user_agent': user_agent,
            }

            # deactivate user session
            user_session = self.user_session_service.logout_user_session(user_session_data)

        except Exception as e:
            logger.error(f"Error logout: {str(e)}")
            raise ServiceError(f"Error logging in: {str(e)}")

    @transaction.atomic
    def register(self, user_payload):
        try:
            company = self.company_service.get_default_company()
            user_payload["company"] = company
            user_payload["is_active"] = False
            email = user_payload.get("email")
            username = user_payload.get("username")

            is_user_registered, message = self.user_service.is_user_registered(email, username)
            if is_user_registered:
                return None, message

            is_registration_completed, user, message = self.user_service.is_registration_completed(email, username)
            if not is_registration_completed:
                return user, message

            user = self.user_service.create_user(user_payload)

            return user, _("Registration successful. Please check your email to verify your account.")

        except Exception as e:
            logger.error(f"Error registering user with email {email}: {str(e)}")
            raise ServiceError(f"Error registering: {str(e)}")

    def refresh_token(self, refresh_token):
        try:
            refresh = RefreshToken(refresh_token)
            tokens = {
                "access": str(refresh.access_token),
                "refresh": str(refresh),
            }
            logger.info("Token refreshed successfully")

            return tokens

        except Exception as e:
            logger.error(f"Error refreshing token: {str(e)}")
            raise ServiceError(f"Error refreshing token: {str(e)}")

    def get_tokens(self, user):
        try:
            refresh = RefreshToken.for_user(user)
            tokens = {
                "access": str(refresh.access_token),
                "refresh": str(refresh)
            }

            return tokens

        except Exception as e:
            logger.error(f"Error generating token: {str(e)}")
            raise ServiceError(f"Error generating token: {str(e)}")